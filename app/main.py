"""Main FastAPI application"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api import pandoc, templates
from app.core.config import settings
from app.core.database import create_tables, get_db
from app.core.template_manager import TemplateManager

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    create_tables()

    # Load templates on startup
    try:
        db = next(get_db())
        template_manager = TemplateManager(db)
        results = await template_manager.scan_and_load_templates()
        logger.info(f"Template loading completed: {results}")
    except Exception as e:
        logger.error(f"Failed to load templates on startup: {e}")

    yield
    # Shutdown
    pass


# Create FastAPI application
app = FastAPI(
    title="Pandoc FastAPI Service / Pandoc FastAPI 服务",
    description="""
    HTTP API service for Pandoc document conversion with template management

    基于Pandoc的文档转换HTTP API服务，支持模板管理

    ## Features / 功能特性

    ### Document Conversion / 文档转换
    - Support for multiple input/output formats / 支持多种输入输出格式
    - Custom DOCX templates / 自定义DOCX模板
    - CSL citation styles / CSL引用样式
    - Custom metadata and options / 自定义元数据和选项

    ### Template Management / 模板管理
    - Upload and manage templates / 上传和管理模板
    - Built-in template library / 内置模板库
    - Template search and filtering / 模板搜索和过滤
    - Support for DOCX, CSL, and Lua filter files / 支持DOCX、CSL和Lua过滤器文件

    ## API Documentation / API文档
    - Interactive API docs: `/docs`
    - Alternative docs: `/redoc`
    """,
    version="0.1.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(pandoc.router, prefix="/api/v1")
app.include_router(templates.router, prefix="/api/v1")


@app.get("/", summary="根路径", description="API服务根路径，返回基本信息")
async def root():
    """
    根路径端点

    Root endpoint

    返回API服务的基本信息和文档链接
    Returns basic information about the API service and documentation links
    """
    return {
        "message": "Pandoc FastAPI Service / Pandoc FastAPI 服务",
        "version": "0.1.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "description": "Document conversion API with template management / 支持模板管理的文档转换API",
    }


@app.get("/health", summary="健康检查", description="检查API服务的健康状态")
async def health_check():
    """
    健康检查端点

    Health check endpoint

    用于监控和负载均衡器检查服务状态
    Used by monitoring and load balancers to check service status
    """
    return {
        "status": "healthy",
        "message": "服务正常运行 / Service is running normally",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
    )
