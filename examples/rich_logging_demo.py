#!/usr/bin/env python3
"""
Demo script showing Rich logging and error handling in the Pandoc FastAPI service
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.logging_config import setup_logging, get_logger, log_success, log_warning, log_error
from app.core.exception_handlers import (
    PandocConversionError,
    TemplateNotFoundError,
    FileProcessingError,
    ValidationError,
    format_exception_for_console,
)


def demo_basic_logging():
    """Demonstrate basic Rich logging features"""
    logger = get_logger("demo.basic")
    
    logger.info("This is a basic info message")
    logger.debug("This is a debug message (may not show depending on log level)")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Rich formatted messages
    logger.info("[bold green]This is a bold green message[/bold green]", extra={"markup": True})
    logger.info("[blue]Processing file:[/blue] [yellow]document.docx[/yellow]", extra={"markup": True})
    
    # Using helper functions
    log_success(logger, "Operation completed successfully")
    log_warning(logger, "This is a warning with icon")
    log_error(logger, "This is an error with icon")


def demo_exception_handling():
    """Demonstrate custom exception handling"""
    logger = get_logger("demo.exceptions")
    
    logger.info("[bold blue]Demonstrating custom exceptions...[/bold blue]", extra={"markup": True})
    
    # Example 1: PandocConversionError
    try:
        raise PandocConversionError(
            message="Failed to convert document from markdown to PDF",
            details={
                "input_format": "markdown",
                "output_format": "pdf",
                "error_code": "latex_not_found"
            }
        )
    except PandocConversionError as e:
        logger.error(f"Conversion error: {e.message}")
        logger.debug(f"Error details: {e.details}")
    
    # Example 2: TemplateNotFoundError
    try:
        raise TemplateNotFoundError("academic-paper", "DOCX template")
    except TemplateNotFoundError as e:
        logger.error(f"Template error: {e.message}")
        logger.debug(f"Template details: {e.details}")
    
    # Example 3: FileProcessingError
    try:
        raise FileProcessingError(
            message="Failed to process uploaded file",
            filename="corrupted_document.docx",
            details={"file_size": "0 bytes", "mime_type": "unknown"}
        )
    except FileProcessingError as e:
        logger.error(f"File processing error: {e.message}")
        logger.debug(f"File details: {e.details}")
    
    # Example 4: ValidationError
    try:
        raise ValidationError(
            message="Invalid JSON format in options field",
            field="options",
            details={"provided_value": "invalid json {", "expected": "valid JSON object"}
        )
    except ValidationError as e:
        logger.error(f"Validation error: {e.message}")
        logger.debug(f"Validation details: {e.details}")


def demo_rich_traceback():
    """Demonstrate Rich traceback formatting"""
    logger = get_logger("demo.traceback")
    
    logger.info("[bold blue]Demonstrating Rich traceback...[/bold blue]", extra={"markup": True})
    
    def nested_function_1():
        def nested_function_2():
            def nested_function_3():
                # This will create a nice traceback
                raise ValueError("This is a test exception with a nested call stack")
            nested_function_3()
        nested_function_2()
    
    try:
        nested_function_1()
    except Exception as e:
        # This will show a beautiful Rich-formatted traceback
        logger.exception("Caught an exception with Rich formatting")
        
        # You can also format it for console display
        print("\n" + "="*50)
        print("Rich Console Traceback:")
        print("="*50)
        format_exception_for_console(e)


async def demo_async_logging():
    """Demonstrate logging in async context"""
    logger = get_logger("demo.async")
    
    logger.info("[bold blue]Starting async operation...[/bold blue]", extra={"markup": True})
    
    # Simulate some async work
    for i in range(3):
        logger.info(f"[cyan]Processing step {i+1}/3[/cyan]", extra={"markup": True})
        await asyncio.sleep(0.5)
        log_success(logger, f"Step {i+1} completed")
    
    log_success(logger, "All async operations completed")


def demo_different_log_formats():
    """Demonstrate different log formats"""
    print("\n" + "="*60)
    print("RICH FORMAT (default)")
    print("="*60)
    
    # Rich format (default)
    setup_logging(format_type="rich")
    logger = get_logger("demo.rich")
    logger.info("This is Rich format with colors and formatting")
    log_success(logger, "Rich format example")
    
    print("\n" + "="*60)
    print("JSON FORMAT")
    print("="*60)
    
    # JSON format
    setup_logging(format_type="json")
    logger = get_logger("demo.json")
    logger.info("This is JSON format for structured logging")
    logger.error("JSON format error example")
    
    print("\n" + "="*60)
    print("STANDARD FORMAT")
    print("="*60)
    
    # Standard format
    setup_logging(format_type="standard")
    logger = get_logger("demo.standard")
    logger.info("This is standard format")
    logger.warning("Standard format warning example")
    
    # Reset to rich format
    setup_logging(format_type="rich")


def main():
    """Main demo function"""
    print("🎨 Rich Logging and Error Handling Demo")
    print("="*50)
    
    # Setup Rich logging
    setup_logging(level="DEBUG", format_type="rich")
    
    # Run demos
    demo_basic_logging()
    print("\n" + "-"*50)
    
    demo_exception_handling()
    print("\n" + "-"*50)
    
    demo_rich_traceback()
    print("\n" + "-"*50)
    
    # Run async demo
    asyncio.run(demo_async_logging())
    print("\n" + "-"*50)
    
    demo_different_log_formats()
    
    print("\n🎉 Demo completed!")


if __name__ == "__main__":
    main()
