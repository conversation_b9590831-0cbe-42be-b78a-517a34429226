# Rich Logging and Error Handling

This project uses [Rich](https://github.com/Textualize/rich) for beautiful, colorful logging and enhanced error handling with formatted tracebacks.

## Features

### 🎨 Rich Logging
- **Colorful output**: Different colors for different log levels
- **Formatted messages**: Support for Rich markup in log messages
- **Icons**: Success (✓), warning (⚠), and error (✗) icons
- **Timestamps**: Automatic timestamp formatting
- **File and line info**: Shows source file and line number
- **Multiple formats**: Rich, JSON, and standard formats

### 🚨 Enhanced Error Handling
- **Custom exceptions**: Structured exception classes with detailed information
- **Rich tracebacks**: Beautiful, readable stack traces with syntax highlighting
- **Local variables**: Shows local variables in debug mode
- **Suppressed modules**: Hides internal framework code for cleaner traces
- **Automatic error logging**: All exceptions are automatically logged with Rich formatting

## Configuration

### Environment Variables

You can configure logging behavior using environment variables:

```bash
# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log format (rich, json, standard)
LOG_FORMAT=rich

# Optional log file path
LOG_FILE=logs/app.log

# Debug mode (shows local variables in tracebacks)
DEBUG=false
```

### Programmatic Configuration

```python
from app.core.logging_config import setup_logging

# Setup with defaults from settings
setup_logging()

# Or override specific settings
setup_logging(
    level="DEBUG",
    format_type="rich",
    log_file="logs/debug.log"
)
```

## Usage Examples

### Basic Logging

```python
from app.core.logging_config import get_logger, log_success, log_warning, log_error

logger = get_logger(__name__)

# Basic logging
logger.info("Processing document")
logger.warning("File size is large")
logger.error("Conversion failed")

# Rich formatted messages
logger.info("[bold blue]Starting conversion[/bold blue]", extra={"markup": True})
logger.info("[green]File:[/green] [yellow]document.docx[/yellow]", extra={"markup": True})

# Helper functions with icons
log_success(logger, "Document converted successfully")
log_warning(logger, "Template not found, using default")
log_error(logger, "Invalid file format")
```

### Custom Exceptions

```python
from app.core.exception_handlers import (
    PandocConversionError,
    TemplateNotFoundError,
    FileProcessingError,
    ValidationError,
)

# Raise structured exceptions
raise PandocConversionError(
    message="Failed to convert document",
    details={
        "input_format": "markdown",
        "output_format": "pdf",
        "error_code": "latex_not_found"
    }
)

raise TemplateNotFoundError("academic-paper", "DOCX template")

raise FileProcessingError(
    message="Failed to process uploaded file",
    filename="document.docx",
    details={"file_size": "0 bytes"}
)

raise ValidationError(
    message="Invalid JSON in options field",
    field="options",
    details={"provided_value": "invalid json"}
)
```

### Exception Handling in API Endpoints

```python
from fastapi import APIRouter
from app.core.logging_config import get_logger, log_success, log_error
from app.core.exception_handlers import PandocConversionError

router = APIRouter()
logger = get_logger(__name__)

@router.post("/convert")
async def convert_document(file: UploadFile):
    try:
        logger.info(f"[bold blue]Starting conversion[/bold blue]: {file.filename}", 
                   extra={"markup": True})
        
        # Your conversion logic here
        result = await convert_file(file)
        
        log_success(logger, f"Conversion completed: {file.filename}")
        return result
        
    except Exception as e:
        log_error(logger, f"Conversion failed: {e}")
        raise PandocConversionError(
            message="Document conversion failed",
            details={"filename": file.filename, "error": str(e)}
        )
```

## Log Formats

### Rich Format (Default)
Beautiful colored output with icons and formatting:
```
[08/04/25 20:36:48] INFO     ✓ Document converted successfully    main.py:42
                    WARNING  ⚠ Template not found, using default  main.py:43
                    ERROR    ✗ Conversion failed                   main.py:44
```

### JSON Format
Structured logging for log aggregation systems:
```json
{
  "timestamp": "2025-08-04T20:36:48.123456",
  "level": "INFO",
  "logger": "app.main",
  "message": "Document converted successfully",
  "module": "main",
  "function": "convert_document",
  "line": 42
}
```

### Standard Format
Traditional log format:
```
2025-08-04 20:36:48 - app.main - INFO - Document converted successfully
2025-08-04 20:36:48 - app.main - WARNING - Template not found, using default
2025-08-04 20:36:48 - app.main - ERROR - Conversion failed
```

## Rich Tracebacks

When exceptions occur, Rich provides beautiful, readable tracebacks:

```
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /app/api/pandoc.py:123 in convert_document                                                        │
│                                                                                                  │
│   120 │   try:                                                                                   │
│   121 │   │   result = await pandoc_runner.convert(file)                                         │
│   122 │   │   return result                                                                      │
│ ❱ 123 │   except Exception as e:                                                                 │
│   124 │   │   raise PandocConversionError(str(e))                                                │
│                                                                                                  │
│ ╭───────────────────────────────── locals ─────────────────────────────────╮                     │
│ │    e = FileNotFoundError('pandoc command not found')                     │                     │
│ │ file = <UploadFile filename='document.md'>                               │                     │
│ ╰──────────────────────────────────────────────────────────────────────────╯                     │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
```

## Demo Script

Run the demo script to see all features in action:

```bash
uv run python examples/rich_logging_demo.py
```

This will demonstrate:
- Basic logging with different levels
- Rich formatted messages
- Custom exception handling
- Rich tracebacks with local variables
- Different log formats (Rich, JSON, Standard)
- Async logging

## Integration with FastAPI

The Rich logging and error handling is automatically integrated with your FastAPI application:

1. **Automatic setup**: Logging is configured when the app starts
2. **Exception handlers**: Custom exception handlers provide structured error responses
3. **Request logging**: All API requests and responses are logged
4. **Error tracking**: All errors are automatically logged with Rich formatting

The integration provides:
- Consistent error response format
- Detailed error logging
- Beautiful console output during development
- Structured logging for production

## Best Practices

1. **Use structured exceptions**: Always use custom exception classes instead of generic exceptions
2. **Include context**: Add relevant details to exception `details` parameter
3. **Log at appropriate levels**: Use DEBUG for detailed info, INFO for general flow, WARNING for issues, ERROR for failures
4. **Use Rich markup**: Take advantage of Rich formatting for better readability
5. **Don't log sensitive data**: Be careful not to log passwords, tokens, or personal information
6. **Use helper functions**: Use `log_success`, `log_warning`, `log_error` for consistent formatting
